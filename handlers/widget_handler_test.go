package handlers

import (
	"bytes"
	"api.appio.so/models"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/middlewares"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupWidgetHandler(t *testing.T) (*WidgetHandler, *mocks.MockWidgetServiceInterface, *mocks.MockWidgetConfigServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockWidgetService := mocks.NewMockWidgetServiceInterface(ctrl)
	mockWidgetConfigService := mocks.NewMockWidgetConfigServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)
	handler := NewWidgetHandler(mockWidgetService, mockWidgetConfigService, logger)
	return handler, mockWidgetService, mockWidgetConfigService, ctrl
}



func addPlatformToWidgetContext(req *http.Request, platform models.Platform) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.PlatformKey{}, platform)
	return req.WithContext(ctx)
}

func TestNewWidgetHandler(t *testing.T) {
	t.Run("Creates widget handler correctly", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		assert.NotNil(t, handler)
		assert.NotNil(t, handler.service)
		assert.NotNil(t, handler.widgetConfigService)
		assert.NotNil(t, handler.logger)
	})
}

func TestWidgetHandler_Get(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Success - widget found", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		expectedWidget := &models.Widget{
			ID:        wgtID,
			ServiceID: svcID,
			WidgetRequest: models.WidgetRequest{
				Template: "Test Widget Template",
			},
		}

		mockWidgetService.EXPECT().
			FindByID(gomock.Any(), svcID, wgtID).
			Return(expectedWidget, nil)

		router := chi.NewRouter()
		router.Get("/widgets/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String(), nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.Widget
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, wgtID, response.ID)
		assert.Equal(t, "Test Widget Template", response.WidgetRequest.Template)
	})

	t.Run("RawError - widget not found", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		mockWidgetService.EXPECT().
			FindByID(gomock.Any(), svcID, wgtID).
			Return(nil, nil)

		router := chi.NewRouter()
		router.Get("/widgets/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String(), nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		mockWidgetService.EXPECT().
			FindByID(gomock.Any(), svcID, wgtID).
			Return(nil, pkg.ErrInternal)

		router := chi.NewRouter()
		router.Get("/widgets/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String(), nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/widgets/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String(), nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - invalid widget ID", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/widgets/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/widgets/invalid-id", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestWidgetHandler_GetConfig(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Success - widget config found", func(t *testing.T) {
		handler, mockWidgetService, mockWidgetConfigService, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widget := &models.Widget{
			ID:        wgtID,
			ServiceID: svcID,
			WidgetRequest: models.WidgetRequest{
				Template: "Test Widget Template",
			},
		}

		expectedConfig := &models.WidgetConfig{
			ID:   wgtID,
			Name: "Test Widget Config",
		}

		mockWidgetService.EXPECT().
			FindByID(gomock.Any(), svcID, wgtID).
			Return(widget, nil)

		mockWidgetConfigService.EXPECT().
			ParseWidgetConfig(models.PlatformIOS, widget).
			Return(expectedConfig, nil)

		router := chi.NewRouter()
		router.Get("/widgets/{id}/config", handler.GetConfig)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String()+"/config", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		req = addPlatformToWidgetContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.WidgetConfig
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, wgtID, response.ID)
		assert.Equal(t, "Test Widget Config", response.Name)
	})

	t.Run("RawError - widget not found", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		mockWidgetService.EXPECT().
			FindByID(gomock.Any(), svcID, wgtID).
			Return(nil, pkg.ErrNotFound)

		router := chi.NewRouter()
		router.Get("/widgets/{id}/config", handler.GetConfig)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String()+"/config", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - config parsing error", func(t *testing.T) {
		handler, mockWidgetService, mockWidgetConfigService, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widget := &models.Widget{
			ID:        wgtID,
			ServiceID: svcID,
			WidgetRequest: models.WidgetRequest{
				Template: "Test Widget Template",
			},
		}

		mockWidgetService.EXPECT().
			FindByID(gomock.Any(), svcID, wgtID).
			Return(widget, nil)

		mockWidgetConfigService.EXPECT().
			ParseWidgetConfig(models.PlatformIOS, widget).
			Return(nil, pkg.ErrInternal)

		router := chi.NewRouter()
		router.Get("/widgets/{id}/config", handler.GetConfig)

		req := httptest.NewRequest("GET", "/widgets/"+wgtID.String()+"/config", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		req = addPlatformToWidgetContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestWidgetHandler_List(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - widgets found", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		expectedWidgets := []models.Widget{
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000001"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "Widget 1 Template",
				},
			},
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000002"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "Widget 2 Template",
				},
			},
		}

		mockWidgetService.EXPECT().
			List(gomock.Any(), svcID).
			Return(expectedWidgets, nil)

		req := httptest.NewRequest("GET", "/widgets", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response []models.Widget
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 2)
		assert.Equal(t, "Widget 1 Template", response[0].WidgetRequest.Template)
		assert.Equal(t, "Widget 2 Template", response[1].WidgetRequest.Template)
	})

	t.Run("Success - empty list", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		expectedWidgets := []models.Widget{}

		mockWidgetService.EXPECT().
			List(gomock.Any(), svcID).
			Return(expectedWidgets, nil)

		req := httptest.NewRequest("GET", "/widgets", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response []models.Widget
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 0)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		mockWidgetService.EXPECT().
			List(gomock.Any(), svcID).
			Return(nil, pkg.ErrInternal)

		req := httptest.NewRequest("GET", "/widgets", nil)
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/widgets", nil)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestWidgetHandler_Create(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Success - widget created", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "New Widget Template",
		}

		mockWidgetService.EXPECT().
			Create(gomock.Any(), svcID, gomock.Any()).
			Return(wgtID, nil)

		reqBody, _ := json.Marshal(widgetReq)
		req := httptest.NewRequest("POST", "/widgets", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, wgtID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/widgets", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "New Widget Template",
		}

		mockWidgetService.EXPECT().
			Create(gomock.Any(), svcID, gomock.Any()).
			Return(nil, pkg.ErrInternal)

		reqBody, _ := json.Marshal(widgetReq)
		req := httptest.NewRequest("POST", "/widgets", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "New Widget Template",
		}

		reqBody, _ := json.Marshal(widgetReq)
		req := httptest.NewRequest("POST", "/widgets", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestWidgetHandler_Update(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Success - widget updated", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "Updated Widget Template",
		}

		mockWidgetService.EXPECT().
			Update(gomock.Any(), svcID, wgtID, gomock.Any()).
			Return(nil)

		reqBody, _ := json.Marshal(widgetReq)
		router := chi.NewRouter()
		router.Put("/widgets/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/widgets/"+wgtID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, wgtID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Put("/widgets/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/widgets/"+wgtID.String(), bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockWidgetService, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "Updated Widget Template",
		}

		mockWidgetService.EXPECT().
			Update(gomock.Any(), svcID, wgtID, gomock.Any()).
			Return(pkg.ErrInternal)

		reqBody, _ := json.Marshal(widgetReq)
		router := chi.NewRouter()
		router.Put("/widgets/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/widgets/"+wgtID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "Updated Widget Template",
		}

		reqBody, _ := json.Marshal(widgetReq)
		router := chi.NewRouter()
		router.Put("/widgets/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/widgets/"+wgtID.String(), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - invalid widget ID", func(t *testing.T) {
		handler, _, _, ctrl := setupWidgetHandler(t)
		defer ctrl.Finish()

		widgetReq := models.WidgetRequest{
			Template: "Updated Widget Template",
		}

		reqBody, _ := json.Marshal(widgetReq)
		router := chi.NewRouter()
		router.Put("/widgets/{id}", handler.Update)

		req := httptest.NewRequest("PUT", "/widgets/invalid-id", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToWidgetContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
