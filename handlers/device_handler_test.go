package handlers

import (
	"api.appio.so/internal/mocks"
	"api.appio.so/models"
	"api.appio.so/middlewares"
	"api.appio.so/pkg"
	"bytes"
	"context"
	"encoding/json"
	"github.com/appio-so/go-appioid"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func setupDeviceHandler(t *testing.T) (*DeviceHandler, *mocks.MockDeviceServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockService := mocks.NewMockDeviceServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)
	handler := NewDeviceHandler(mockService, logger)
	return handler, mockService, ctrl
}



func TestNewDeviceHandler(t *testing.T) {
	t.Run("Creates device handler correctly", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		assert.NotNil(t, handler)
		assert.NotNil(t, handler.service)
		assert.NotNil(t, handler.logger)
	})
}

func TestDeviceHandler_Get(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - device found", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		expectedDevice := &models.DeviceResponse{
			DeviceRecord: models.DeviceRecord{
				ID: dvcID,
				DeviceData: models.DeviceData{
					Name:          "Test Device",
					Platform:      models.PlatformIOS,
					MarketingName: "iPhone",
				},
			},
		}

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(expectedDevice, nil)

		router := chi.NewRouter()
		router.Get("/devices/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/devices/"+dvcID.String(), nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.DeviceResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, expectedDevice.DeviceRecord.ID, response.DeviceRecord.ID)
		assert.Equal(t, expectedDevice.DeviceRecord.DeviceData.Name, response.DeviceRecord.DeviceData.Name)
	})

	t.Run("RawError - device not found", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(nil, pkg.ErrNotFound)

		router := chi.NewRouter()
		router.Get("/devices/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/devices/"+dvcID.String(), nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service returns nil device", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			FindByID(gomock.Any(), svcID, dvcID).
			Return(nil, nil)

		router := chi.NewRouter()
		router.Get("/devices/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/devices/"+dvcID.String(), nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/devices/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/devices/"+dvcID.String(), nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - invalid device ID", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Get("/devices/{id}", handler.Get)

		req := httptest.NewRequest("GET", "/devices/invalid-id", nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestDeviceHandler_List(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - devices found", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		expectedDevices := []models.DeviceResponse{
			{
				DeviceRecord: models.DeviceRecord{
					ID: appioid.MustParse("dvc_00000000000000000000000001"),
					DeviceData: models.DeviceData{
						Name:          "Device 1",
						Platform:      models.PlatformIOS,
						MarketingName: "iPhone",
					},
				},
			},
			{
				DeviceRecord: models.DeviceRecord{
					ID: appioid.MustParse("dvc_00000000000000000000000002"),
					DeviceData: models.DeviceData{
						Name:          "Device 2",
						Platform:      models.PlatformAndroid,
						MarketingName: "Android Device",
					},
				},
			},
		}

		mockService.EXPECT().
			List(gomock.Any(), svcID).
			Return(expectedDevices, nil)

		req := httptest.NewRequest("GET", "/devices", nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response []models.DeviceResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Len(t, response, 2)
		assert.Equal(t, expectedDevices[0].DeviceRecord.ID, response[0].DeviceRecord.ID)
		assert.Equal(t, expectedDevices[1].DeviceRecord.ID, response[1].DeviceRecord.ID)
	})

	t.Run("Success - empty list", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			List(gomock.Any(), svcID).
			Return([]models.DeviceResponse{}, nil)

		req := httptest.NewRequest("GET", "/devices", nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "[]", w.Body.String())
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		mockService.EXPECT().
			List(gomock.Any(), svcID).
			Return(nil, pkg.ErrInternal)

		req := httptest.NewRequest("GET", "/devices", nil)
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("GET", "/devices", nil)
		w := httptest.NewRecorder()

		handler.List(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestDeviceHandler_CreateAndLink(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - device created", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		deviceReq := models.DeviceCreateRequest{
			CustomerUserID: "user123",
			DeviceData: models.DeviceData{
				Name:                 "Test Device",
				Platform:             models.PlatformIOS,
				OsVersion:            "17.0",
				Model:                "iPhone15,2",
				DeviceToken:          "token123",
				NotificationsEnabled: true,
				DeviceIdentifier:     "device123",
			},
		}

		mockService.EXPECT().
			CreateAndLink(gomock.Any(), svcID, deviceReq).
			Return(dvcID, nil)

		reqBody, _ := json.Marshal(deviceReq)
		req := httptest.NewRequest("POST", "/devices", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.CreateAndLink(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, dvcID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/devices", bytes.NewBufferString("invalid json"))
		req.Header.Set("Content-Type", "application/json")
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.CreateAndLink(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - validation fails", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		deviceReq := models.DeviceCreateRequest{
			CustomerUserID: "", // Missing required field
			DeviceData: models.DeviceData{
				Name: "", // Missing required field
			},
		}

		reqBody, _ := json.Marshal(deviceReq)
		req := httptest.NewRequest("POST", "/devices", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.CreateAndLink(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		deviceReq := models.DeviceCreateRequest{
			CustomerUserID: "user123",
			DeviceData: models.DeviceData{
				Name:                 "Test Device",
				Platform:             models.PlatformIOS,
				OsVersion:            "17.0",
				Model:                "iPhone15,2",
				DeviceToken:          "token123",
				NotificationsEnabled: true,
				DeviceIdentifier:     "device123",
			},
		}

		mockService.EXPECT().
			CreateAndLink(gomock.Any(), svcID, deviceReq).
			Return(nil, pkg.ErrInternal)

		reqBody, _ := json.Marshal(deviceReq)
		req := httptest.NewRequest("POST", "/devices", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		handler.CreateAndLink(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

func TestDeviceHandler_LinkWithService(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - device linked with service", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		linkReq := models.DeviceLinkServiceRequest{
			CustomerUserID: "user123",
		}

		mockService.EXPECT().
			LinkWithService(gomock.Any(), svcID, dvcID, linkReq).
			Return(nil)

		router := chi.NewRouter()
		router.Post("/devices/{id}/services", handler.LinkWithService)

		reqBody, _ := json.Marshal(linkReq)
		req := httptest.NewRequest("POST", "/devices/"+dvcID.String()+"/services", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, dvcID, response.ID)
	})

	t.Run("RawError - empty customer_user_id", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		linkReq := models.DeviceLinkServiceRequest{
			CustomerUserID: "", // Empty customer_user_id
		}

		router := chi.NewRouter()
		router.Post("/devices/{id}/services", handler.LinkWithService)

		reqBody, _ := json.Marshal(linkReq)
		req := httptest.NewRequest("POST", "/devices/"+dvcID.String()+"/services", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Verify the error response contains the expected validation error
		var errorResponse map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
		assert.NoError(t, err)

		// Check the nested structure: error.data.customer_user_id
		errorData, ok := errorResponse["error"].(map[string]interface{})
		assert.True(t, ok, "Expected 'error' field in response")

		data, ok := errorData["data"].(map[string]interface{})
		assert.True(t, ok, "Expected 'data' field in error")

		assert.Contains(t, data, "customer_user_id")
		assert.Equal(t, "customer_user_id is required", data["customer_user_id"])
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		router := chi.NewRouter()
		router.Post("/devices/{id}/services", handler.LinkWithService)

		req := httptest.NewRequest("POST", "/devices/"+dvcID.String()+"/services", bytes.NewBufferString("invalid json"))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		linkReq := models.DeviceLinkServiceRequest{
			CustomerUserID: "user123",
		}

		mockService.EXPECT().
			LinkWithService(gomock.Any(), svcID, dvcID, linkReq).
			Return(pkg.ErrInternal)

		router := chi.NewRouter()
		router.Post("/devices/{id}/services", handler.LinkWithService)

		reqBody, _ := json.Marshal(linkReq)
		req := httptest.NewRequest("POST", "/devices/"+dvcID.String()+"/services", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - missing service ID", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		linkReq := models.DeviceLinkServiceRequest{
			CustomerUserID: "user123",
		}

		router := chi.NewRouter()
		router.Post("/devices/{id}/services", handler.LinkWithService)

		reqBody, _ := json.Marshal(linkReq)
		req := httptest.NewRequest("POST", "/devices/"+dvcID.String()+"/services", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - invalid device ID", func(t *testing.T) {
		handler, _, ctrl := setupDeviceHandler(t)
		defer ctrl.Finish()

		linkReq := models.DeviceLinkServiceRequest{
			CustomerUserID: "user123",
		}

		router := chi.NewRouter()
		router.Post("/devices/{id}/services", handler.LinkWithService)

		reqBody, _ := json.Marshal(linkReq)
		req := httptest.NewRequest("POST", "/devices/invalid-id/services", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = addServiceIDToContext(req, svcID)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
